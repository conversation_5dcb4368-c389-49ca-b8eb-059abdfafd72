import os
import uuid
from datetime import datetime
import random

def make_narration_path(
    output_dir: str = "bp_shorts_narration",
    prefix: str = "narration",
    ext: str = "mp3",
) -> str:
    """
    Ensure `output_dir` exists, then build and return a filename of the form:
      {prefix}_{YYYYMMDD}_{UUID}.{ext}
    """
    # create folder if needed
    os.makedirs(output_dir, exist_ok=True)

    # build date + uuid
    today_str = datetime.now().strftime("%Y%m%d")
    unique_id = uuid.uuid4().hex

    # assemble and return full path
    filename = f"{prefix}_{today_str}_{unique_id}.{ext}"
    return os.path.join(output_dir, filename)

def get_random_audio_with_today_date():
    """Get a random audio file from bp_shorts_narration that contains today's date"""
    today_date = datetime.now().strftime("%Y%m%d")
    narration_folder = "bp_shorts_narration"

    if not os.path.exists(narration_folder):
        raise FileNotFoundError(f"Narration folder '{narration_folder}' not found")

    # Get all audio files that contain today's date
    audio_files = [
        f for f in os.listdir(narration_folder)
        if f.endswith('.mp3') and today_date in f
    ]

    if not audio_files:
        raise FileNotFoundError(f"No audio files found with today's date ({today_date}) in {narration_folder}")

    # Select a random file and return the full path
    selected_file = random.choice(audio_files)
    return os.path.join(narration_folder, selected_file)