import os, requests, datetime, json

# https://newsapi.org/account
API_KEY = os.getenv("NEWSAPI_KEY")
assert API_KEY, "Set NEWSAPI_KEY"

def latest_blackpink_articles(limit: int = 5, days_back: int = 3):
    since = (datetime.datetime.utcnow() - datetime.timedelta(days=days_back)).isoformat("T") + "Z"
    url = (
        "https://newsapi.org/v2/everything?"
        f"q=BLACKPINK&from={since}&language=en&sortBy=publishedAt"
        f"&pageSize={limit}&apiKey={API_KEY}"
    )
    resp = requests.get(url, timeout=10)
    resp.raise_for_status()
    return resp.json().get("articles", [])

def save_articles_to_file(articles):
    """Save articles to bp_articles folder with date-based filename"""
    # Create bp_articles directory if it doesn't exist
    os.makedirs("bp_articles", exist_ok=True)

    # Generate filename with current date
    current_date = datetime.datetime.now().strftime("%Y%m%d")
    filename = f"bp_articles/articles_{current_date}.json"

    # Save articles to file
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(articles, f, indent=2, ensure_ascii=False)

    return filename

def load_articles_from_file(filename):
    """Load articles from a saved file"""
    with open(filename, 'r', encoding='utf-8') as f:
        return json.load(f)
