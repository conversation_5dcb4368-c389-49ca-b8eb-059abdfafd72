import os, openai
from news_collector import load_articles_from_file

client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

TEMPLATE = """Write a 60-second YouTube Shorts voice-over script with:
1. Choose one article and a one-sentence hook.
2. Catchy headline (≤15 words each).
3. Commentary on the article
4. A fun outro for BLINKs, and remind to like, comment, and subscribe.
Make sure it fills 50 seconds of narration and the returned text contains only what’s read aloud.
"""

def make_script(articles):
    bullet_pts = "\n".join(f"- {a['title']}" for a in articles)
    response = client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[{"role": "user", "content": TEMPLATE + bullet_pts}],
    )
    return response.choices[0].message.content.strip()

def make_script_from_file(articles_filename):
    """Create script from saved articles file"""
    articles = load_articles_from_file(articles_filename)
    return make_script(articles)
