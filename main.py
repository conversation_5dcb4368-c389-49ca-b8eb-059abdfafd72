import logging
import os
import random
import datetime
from news_collector import latest_blackpink_articles, save_articles_to_file
from summariser import make_script_from_file
from naming_utils import make_narration_path
from tts_service import tts
from broll_fetcher import fetch_broll
from video_composer import compose
#from uploader import upload

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def handler():
    # Fetch and save articles to bp_articles folder
    arts = latest_blackpink_articles()
    articles_filename = save_articles_to_file(arts)
    logging.info(f"Articles saved to {articles_filename}")

    # Generate script from saved articles
    script = make_script_from_file(articles_filename)
    logging.info("Script generated from saved articles")

    audio = tts(script, make_narration_path())
    logging.info("Audio generation completed")

    fetch_broll()
    logging.info("B-roll fetching completed")

    # Select random audio file with today's date
    random_audio_path = get_random_audio_with_today_date()
    logging.info(f"Selected audio file: {random_audio_path}")

    video = compose("BP_broll", random_audio_path, "short.mp4")
    logging.info("Video composition completed")

    #upload(video, title="BLACKPINK News Update")

if __name__ == "__main__":
    handler()
